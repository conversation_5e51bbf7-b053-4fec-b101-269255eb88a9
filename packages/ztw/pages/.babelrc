{
  "sourceMaps": false,
  "presets": ["@babel/preset-env", "@babel/preset-react"],
  "plugins": [
    "@babel/plugin-transform-runtime",
    "react-hot-loader/babel",
    // Stage 2 https://github.com/babel/babel/tree/master/packages/babel-preset-stage-2
    [
      "@babel/plugin-proposal-decorators",
      {
        "legacy": true
      }
    ],
    // [  "@babel/plugin-proposal-private-methods",
    //   {
    //     "loose": true
    //   }
    // ],
    "@babel/plugin-proposal-function-sent",
    "@babel/plugin-transform-export-namespace-from",
    "@babel/plugin-transform-numeric-separator",
    "@babel/plugin-proposal-throw-expressions",
    // Stage 3
    "@babel/plugin-syntax-dynamic-import",
    "@babel/plugin-syntax-import-meta",
    [
      "@babel/plugin-transform-class-properties",
      {
        "loose": false
      }
    ],
    "@babel/plugin-transform-json-strings"
  ],
  "env": {
    "test": {
      "plugins": ["istanbul"]
    }
  }
}

{"compilerOptions": {"baseUrl": ".", "paths": {"components/*": ["src/components/*"], "ducks/*": ["src/ducks/*"], "utils/*": ["src/utils/*"], "pages/*": ["src/pages/*"], "layout/*": ["src/layout/*"], "providers/*": ["src/providers/*"], "config/*": ["src/config/*"], "images/*": ["src/images/*"], "scss/*": ["src/scss/*"]}, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "react-jsx"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "build"]}
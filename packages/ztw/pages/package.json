{"name": "@ztw/pages", "version": "1.0.1", "description": "Dual-purpose React library - standalone app and reusable components for Edge Connector UI", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"start": "cross-env NODE_ENV=development webpack-dev-server", "build-dev": "cross-env NODE_ENV=development webpack", "build-old": "npm run lint && npm run test:coverage && npm run prod", "build-lib": "webpack --config webpack/webpack.library.config.js --mode production", "build": "webpack --config webpack/webpack.library.config.js --mode production", "prepublishOnly": "npm run build-lib", "test-lib": "npm run build-lib && npm run validate", "validate": "node scripts/validate-build.js", "analyze": "npm run build-lib && npx webpack-bundle-analyzer dist/index.js", "prod": "cross-env NODE_ENV=production webpack", "svn": "cross-env NODE_ENV=svn webpack --max-old-space-size=8192", "mini": "cross-env NODE_ENV=mini NODE_OPTIONS=--max-old-space-size=8192 webpack", "build:oneui": "cross-env REACT_APP_BUILD_TARGET=oneui INLINE_RUNTIME_CHUNK=false NODE_ENV=oneui NODE_OPTIONS=--max-old-space-size=8192 webpack", "cpgensrc": "cross-env NODE_ENV=cpgensrc webpack", "cptomcat": "cross-env NODE_ENV=copy webpack", "storybook": "start-storybook", "test": "cross-env NODE_ENV=test jest --config=jest.config.js", "test:one": "cross-env NODE_ENV=test NODE_PATH=./src mocha --require @babel/register --require ignore-styles --require ./test/helpers.js --require ./test/dom.js './src/ducks/myProfile/index.test.j*'", "test:coverage": "nyc npm test", "lint": "eslint --fix 'src/**/*.js' 'src/**/*.jsx'", "stats": "webpack-bundle-analyzer dist/stats.json", "compile:translation": "node ./scripts/translate.js", "sort-translations": "node ./scripts/sortTranslations.js"}, "repository": "http://svn.corp.zscaler.com/svn/repos/sm/branches/trunk_adminui_react/userinterface/zmanage/zmanage-r/", "author": "ecui <<EMAIL>> (http://zscaler.com)", "license": "ISC", "engines": {"node": ">=8", "npm": ">=3"}, "jest": {"verbose": true, "rootDir": "src", "setupFiles": ["./setupTests.js"], "coveragePathIgnorePatterns": ["/node_modules/", "/utils/i18n/"], "testPathIgnorePatterns": ["/node_modules/"]}, "dependencies": {"@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-proposal-function-sent": "^7.27.1", "@babel/plugin-proposal-throw-expressions": "^7.27.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-runtime": "^7.27.3", "@eslint/js": "^9.27.0", "@fortawesome/fontawesome-pro": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/pro-light-svg-icons": "^6.7.2", "@fortawesome/pro-regular-svg-icons": "^6.7.2", "@fortawesome/pro-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@nivo/bar": "^0.88.0", "@nivo/core": "^0.88.0", "@nivo/pie": "^0.88.0", "@nivo/tooltip": "^0.88.0", "@openreplay/tracker": "^6.0.2", "@reduxjs/toolkit": "^2.5.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^12.1.5", "@zs-nimbus/dataviz-colors": "^1.2.1", "@zs-nimbus/foundations": "^1.4.1", "@zscaler/ec-domain": "1.0.0-63", "axios": "^0.19.2", "axios-jsonp": "^1.0.4", "babel-eslint": "10.0.1", "babel-loader": "^8.4.1", "brace": "^0.11.1", "core-js": "^3.42.0", "csvtojson": "^2.0.10", "dayjs": "1.10.6", "globals": "^15.15.0", "html2canvas": "^1.4.1", "i18next": "^15.1.3", "ignore-styles": "^5.0.1", "inter-ui": "^3.19.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^25.0.1", "jspdf": "^2.5.2", "leaflet": "^1.9.4", "leaflet.markercluster": "^1.5.3", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lodash.clonedeep": "^4.5.0", "mixpanel-browser": "^2.65.0", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "node-polyfill-webpack-plugin": "^2.0.1", "prop-types": "^15.8.1", "rc-time-picker": "^3.7.3", "react": "^18.3.1", "react-app-polyfill": "^2.0.0", "react-beautiful-dnd": "13.1.0", "react-csv": "^1.1.2", "react-data-grid": "6.1.0", "react-data-grid-addons": "7.0.0-alpha.24", "react-datepicker": "^4.25.0", "react-diff-viewer": "3.1.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-hot-loader": "^4.13.1", "react-html-id": "^0.1.5", "react-i18next": "^10.13.2", "react-leaflet": "^2.8.0", "react-leaflet-markercluster": "^2.0.0", "react-modal": "3.12.1", "react-redux": "^7.2.9", "react-resizable": "3.0.4", "react-router-dom": "^6.30.1", "react-select": "^2.4.4", "react-simple-maps": "^0.12.1", "react-slick": "^0.25.2", "react-sortable-hoc": "1.9.1", "react-table": "7.7.0", "react-tooltip": "4.2.21", "react-window": "1.8.6", "react-window-infinite-loader": "^1.0.10", "recharts": "2.1.9", "redux": "4.0.1", "redux-debounced": "^0.5.0", "redux-form": "^8.3.10", "redux-promise": "0.6.0", "redux-thunk": "^2.4.2", "regenerator-runtime": "^0.13.11", "reselect": "^4.1.8", "sass": "^1.89.0", "sax": "^1.4.1", "slick-carousel": "^1.8.1", "unused-webpack-plugin": "^2.4.0", "url-loader": "^4.1.1"}, "devDependencies": {"@babel/core": "^7.27.3", "@babel/eslint-parser": "^7.27.1", "@babel/eslint-plugin": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/register": "^7.27.1", "@babel/runtime": "7.26.10", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.16", "@testing-library/react-hooks": "^8.0.1", "ace-diff": "^3.0.3", "ajv": "^8.17.1", "chai": "^4.5.0", "clean-webpack-plugin": "1.0.0", "compression-webpack-plugin": "^4.0.1", "copy-webpack-plugin": "11.0.0", "cross-env": "5.2.0", "css-loader": "^2.1.1", "dotenv-webpack": "^6.0.4", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "1.7.1", "eslint": "^8.57.1", "eslint-config-airbnb": "19.0.4", "eslint-plugin-flowtype": "^3.13.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-mocha": "^10.5.0", "eslint-plugin-react": "^7.37.5", "eslint-webpack-plugin": "^3.2.0", "file-loader": "^6.2.0", "git-revision-webpack-plugin": "^5.0.0", "html-webpack-plugin": "^5.6.3", "husky": "1.3.1", "identity-obj-proxy": "3.0.0", "lint-staged": "8.1.0", "mini-css-extract-plugin": "0.5.0", "mocha": "^6.2.3", "nyc": "^15.1.0", "react-refresh": "^0.14.2", "react-test-renderer": "^16.14.0", "resolve-url-loader": "^5.0.0", "sass-loader": "13.3.2", "sinon": "^7.5.0", "style-loader": "^0.23.1", "swc-loader": "^0.2.6", "terser-webpack-plugin": "^5.3.14", "webpack": "^5.99.9", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^4.10.0", "webpack-dev-server": "4.15.1", "webpack-dynamic-public-path": "^1.0.8", "webpack-merge": "^4.2.2", "webpack-obfuscator": "^2.6.0", "workbox-webpack-plugin": "^5.1.4"}, "peerDependencies": {"@material-ui/core": ">=4.0.0", "@material-ui/icons": ">=4.0.0", "i18next": ">=15.0.0", "react": ">=18.0.0", "react-dom": ">=18.0.0", "react-i18next": ">=10.0.0", "react-redux": ">=7.0.0", "react-router-dom": ">=6.0.0", "redux": ">=4.0.0", "redux-form": "^8.3.0"}, "peerDependenciesMeta": {"react-router-dom": {"optional": true}, "i18next": {"optional": true}, "redux-form": {"optional": true}, "react-i18next": {"optional": true}, "@material-ui/core": {"optional": true}, "@material-ui/icons": {"optional": true}}, "publishConfig": {"registry": "https://nexus.corp.zscaler.com/repository/edge-ux/"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}, "lint-staged": {"*.{js,jsx}": ["eslint --fix"]}}
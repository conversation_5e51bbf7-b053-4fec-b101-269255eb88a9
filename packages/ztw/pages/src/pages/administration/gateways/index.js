import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { withTranslation } from 'react-i18next';
import { noop } from 'utils/lodash';
import AddNewButton from 'components/addNewButton';
import Modal from 'components/modal';
import Loading from 'components/spinner/Loading';
import DeleteConfirmationForm from 'components/DeleteConfirmationForm';
import NavTabs from 'components/navTabs';
import SimpleSearchInput from 'components/Input/SimpleSearchInput';
import RBAC from 'components/rbac';
import PermissionRequired from 'components/PermissionRequired';
import SubscriptionRequired from 'components/subscriptionRequired';
import {
  hasBsku, hasCsku, getReadOnly, verifyConfigData,
} from 'utils/helpers';
import ServerError from 'components/errors/ServerError';
import * as constants from 'ducks/login/constants';
import * as GatewaySelectors from 'ducks/gateways/selectors';
import * as loginSelectors from 'ducks/login/selectors';
import { GATEWAY_TABLE_CONFIGS } from 'ducks/gateways/constants';
import ConfigTable from 'components/configTable';
import { HELP_ARTICLES } from 'config';
// import { BASE_LAYOUT } from 'config';
import HelpArticle from 'components/HelpArticle';

import {
  loadGatewaysData,
  toggleDeleteForm,
  deleteGateways,
  toggleForm,
  toggleViewModal,
  handleOnSearchFilter,
} from 'ducks/gateways';
import {
  GatewayForm,
  GatewaysViewModal,
} from './components';

export class Gateways extends Component {
  static propTypes = {
    accessPermissions: PropTypes.shape({}),
    accessSubscriptions: PropTypes.arrayOf(PropTypes.string),
    authType: PropTypes.string,
    configData: PropTypes.shape(),
    formTitle: PropTypes.string,
    gatewaystabledata: PropTypes.arrayOf(PropTypes.shape()),
    handleDelete: PropTypes.func,
    handleSearch: PropTypes.func,
    handleViewGatewaysForm: PropTypes.func,
    load: PropTypes.func,
    modalLoading: PropTypes.bool,
    searchData: PropTypes.string,
    selectedRowID: PropTypes.string,
    showDeleteForm: PropTypes.bool,
    showForm: PropTypes.bool,
    showViewForm: PropTypes.bool,
    t: PropTypes.func,
    toggleDeleteConfirmationForm: PropTypes.func,
    toggleGatewayForm: PropTypes.func,
  };

  static defaultProps = {
    accessPermissions: {},
    accessSubscriptions: [],
    authType: '',
    configData: {},
    formTitle: '',
    gatewaystabledata: null,
    handleDelete: noop,
    handleViewGatewaysForm: noop,
    load: noop,
    modalLoading: false,
    searchData: '',
    selectedRowID: null,
    showDeleteForm: false,
    showForm: false,
    showViewForm: false,
    t: (str) => str,
    toggleDeleteConfirmationForm: noop,
    toggleGatewayForm: noop,
  };

  componentDidMount() {
    const { load } = this.props;
    load();
  }

  getTableData = () => {
    const {
      gatewaystabledata, accessPermissions, authType, searchData,
    } = this.props;
    const isReadOnly = getReadOnly(accessPermissions.EDGE_CONNECTOR_FORWARDING, authType);
  
    const tableData = gatewaystabledata.filter(
      (row) => searchData === ''
      || row.name.toLowerCase().includes(searchData.toLowerCase())
      || row.primaryType.toLowerCase().includes(searchData.toLowerCase())
      || row.secondaryType.toLowerCase().includes(searchData.toLowerCase())
      || (row.primaryType !== 'AUTO' && row.manualPrimary && row.manualPrimary.toLowerCase().includes(searchData.toLowerCase()))
      || (row.secondaryType !== 'AUTO' && row.manualSecondary && row.manualSecondary.toLowerCase().includes(searchData.toLowerCase())),
    ).map((row) => {
      return {
        id: row.id,
        name: row.name,
        primaryType: row.primaryType,
        secondaryType: row.secondaryType,
        failClosed: row.failClosed,
        description: row.description,
        manualPrimary: row.manualPrimary,
        manualSecondary: row.manualSecondary,
        subcloudSecondary: row.subcloudSecondary,
        subcloudPrimary: row.subcloudPrimary,
        isReadOnly,
        isDeletable: row.name !== 'Default ZIA Gateway' && !isReadOnly,
        isEditable: !isReadOnly,
      };
    });
    return tableData;
  };

  render() {
    const {
      accessPermissions,
      accessSubscriptions,
      authType,
      configData,
      formTitle,
      handleDelete,
      handleSearch,
      handleViewGatewaysForm,
      modalLoading,
      selectedRowID,
      showDeleteForm,
      showForm,
      showViewForm,
      t,
      toggleDeleteConfirmationForm,
      toggleGatewayForm,
    } = this.props;
    const isReadOnly = getReadOnly(accessPermissions.EDGE_CONNECTOR_FORWARDING, authType);
    const hasBSubscription = hasBsku(accessSubscriptions);
    const hasCSubscription = hasCsku(accessSubscriptions);

    if (!hasBSubscription && !hasCSubscription) {
      return <SubscriptionRequired accessSubscriptions={accessSubscriptions} />;
    }

    const disableDnsGateway = verifyConfigData({ configData, key: 'disableDnsGateway' });

    if (accessPermissions.EDGE_CONNECTOR_FORWARDING === 'NONE') {
      return <PermissionRequired accessPermissions={accessPermissions} />;
    }

    return (
      <div className="main-container main-container-gateways">
        <div className="page-title header-3">
          {t('INTERNET_ACCESS_GATEWAY')}
          {/* <NavTabs
            isPageHeader
            tabConfiguration={[{
              id: 'zia-gateways',
              title: t('ZIA_GATEWAY'),
              to: `${BASE_LAYOUT}/administration/gateways`,
            },
            {
              id: 'log-and-control-gateways',
              title: t('LOG_AND_CONTROL_GATEWAY'),
              to: `${BASE_LAYOUT}/administration/log-and-control-gateways`,
            },
            ...(disableDnsGateway ? [] : [{
              id: 'log-and-control-gateways',
              title: t('DNS_GATEWAY'),
              to: `${BASE_LAYOUT}/administration/dns-gateways`,
            }])]} /> */}
        </div>
        <div className="gateways-fragment">
          <div className="grid-toolbar-left">
            {!isReadOnly && <AddNewButton label={t('ADD_INTERNET_ACCESS_GATEWAY')} clickCallback={() => toggleGatewayForm(null, true, 'ADD_INTERNET_ACCESS_GATEWAY')} />}
          </div>
          <div className="grid-toolbar-right">
            <SimpleSearchInput
              withButton
              onKeyPressCb={handleSearch} />
          </div>
        </div>
        <div className="gateways-wrapper zia-gateways">
          <RBAC privilege={constants.PERMISSION_API_KEYS.EDGE_CONNECTOR_FORWARDING}>
            <Loading {...this.props}>
              <HelpArticle article={HELP_ARTICLES.ZIA_GATEWAY} />
              <ServerError {...this.props}>
                <ConfigTable
                  {...GATEWAY_TABLE_CONFIGS}
                  onHandleRowEdit={toggleGatewayForm}
                  onHandleRowDelete={toggleDeleteConfirmationForm}
                  onHandleRowView={handleViewGatewaysForm}
                  data={this.getTableData()} />

                <Modal
                  title={t(formTitle)}
                  isOpen={showForm}
                  styleClass="gateway-modal"
                  closeModal={() => toggleGatewayForm(null, false)}>
                  <GatewayForm />
                </Modal>
                <Modal
                  title={t('VIEW_GATEWAYS')}
                  isOpen={showViewForm}
                  closeModal={() => handleViewGatewaysForm(null, false)}>
                  <GatewaysViewModal />
                </Modal>
                <Modal
                  title={t('DELETE_CONFIRMATION')}
                  isOpen={showDeleteForm}
                  closeModal={() => toggleDeleteConfirmationForm(null, false)}>
                  <DeleteConfirmationForm
                    modalLoading={modalLoading}
                    selectedRowID={selectedRowID}
                    handleCancel={toggleDeleteConfirmationForm}
                    handleDelete={handleDelete} />
                </Modal>
              </ServerError>
            </Loading>
          </RBAC>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state) => ({
  ...GatewaySelectors.baseSelector(state),
  modalLoading: GatewaySelectors.modalLoadingSelector(state),
  selectedRowID: GatewaySelectors.selectedRowIDSelector(state),
  accessPermissions: loginSelectors.accessPermissionsSelector(state),
  authType: loginSelectors.authTypeSelector(state),
  accessSubscriptions: loginSelectors.accessSubscriptionSelector(state),
  configData: loginSelectors.configDataSelector(state),
});

const mapDispatchToProps = (dispatch) => {
  const actions = bindActionCreators({
    load: loadGatewaysData,
    toggleGatewayForm: toggleForm,
    toggleDeleteConfirmationForm: toggleDeleteForm,
    handleDelete: deleteGateways,
    handleViewGatewaysForm: toggleViewModal,
    handleSearch: handleOnSearchFilter,
  }, dispatch);
  return actions;
};

export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(Gateways));

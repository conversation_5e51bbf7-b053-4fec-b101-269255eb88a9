/* eslint-disable import/no-named-as-default-member */
import { get, isEmpty } from 'utils/lodash';
import i18n from 'utils/i18n';
import moment from 'moment-timezone';
import 'moment/locale/de';
import 'moment/locale/es';
import 'moment/locale/fr';
import 'moment/locale/ja';
import 'moment/locale/zh-cn';

import { REACT_APP_CSRF_ENABLED } from 'config';
import { setPasswordExpirySettings } from 'ducks/passwordExpiry';
import { checkActivation } from 'ducks/activation';

import {
  createAction,
  loading,
  loadSuccess,
  loadError,
} from 'ducks/generics';
import PersistentStorage, {
  LS_CSRF_TOKEN,
  LS_LOCALE,
  LS_LOGOUT,
  LS_USERNAME,
} from 'utils/persistentStorage';
import { genericInterface } from 'utils/http';
import { notify, notifyError, notifyPersistent } from 'ducks/notification';

import { generateApiKey, limitedPermission } from 'utils/helpers';
import * as constants from './constants';
import actionTypes from './action-types';

export { REDUCER_KEY } from './constants';

const actionTypeExists = Object.keys(actionTypes).reduce((hash, key) => ({
  ...hash,
  [actionTypes[key]]: true,
}), {});

export const reducer = (state = constants.DEFAULT_STATE, action = {}) => {
  if (actionTypeExists[action.type]) {
    return {
      ...state,
      ...action.payload,
    };
  }
  return state;
};

export default reducer;

const boundLoading = loading(actionTypes.DATA_LOADING);
const boundSuccess = loadSuccess(actionTypes.DATA_LOAD_SUCCESS);
export const boundError = loadError(actionTypes.DATA_LOAD_ERROR);
const dataChanged = createAction(actionTypes.DATA_CHANGED);
const loginApi = genericInterface(constants.API_ENDPOINT);

export const saveCsrfToken = (response) => {
  const csrfToken = get(response, 'headers.x-csrf-token', 'Fetch');
  if (csrfToken !== 'Fetch') {
    PersistentStorage.setItem({ [LS_CSRF_TOKEN]: csrfToken });
  }
};

export const getCSRFToken = (dispatch) => {
  PersistentStorage.removeItem(LS_CSRF_TOKEN);
  if (REACT_APP_CSRF_ENABLED) {
    return loginApi.read(null)
      .then((csrfResponse) => {
        if (csrfResponse) {
          saveCsrfToken(csrfResponse);
          dispatch({
            type: actionTypes.DATA_LOADING,
            payload: {
              [constants.DATA]: { ...csrfResponse.data },
            },
          });
        }
      });
  }
  return Promise.resolve(true);
};

export const getOrgProvisioningInfo = () => async (dispatch) => {
  dispatch(dataChanged({
    loading: true,
  }));
  try {
    const response = await genericInterface(
      constants.ORG_PROV_API,
    ).read();
    const { data } = response || {};
    const { cloudNSSInfo } = data;
    localStorage.setItem('orgProvInfo', JSON.stringify(data));
    dispatch(dataChanged({
      [constants.CLOUD_NSS_PROVISIONED]: !!(cloudNSSInfo && cloudNSSInfo.firewallEnabled),
      loading: false,
    }));
  } catch (error) {
    const errorMsg = get(error, 'response.data.message');
    dispatch(dataChanged({
      loading: false,
    }));
    dispatch(boundError(errorMsg));
    dispatch(notifyError('errorMsg'));
  }
};

export const login = (username, password) => async (dispatch) => {
  dispatch(boundLoading({ loading: true }));
  localStorage.removeItem('authenticated');
  localStorage.removeItem('configData');
  localStorage.removeItem('apiPermissions');
  localStorage.removeItem('loginType');
  PersistentStorage.setItem({ [LS_USERNAME]: username });
  const configDataApi = genericInterface(constants.CONFIG_DATA_API_ENDPOINT);
  const permissionsApi = genericInterface(constants.PERMISSIONS_API_ENDPOINT);
  const subscriptionApi = genericInterface(constants.SUBSCRIPTIONS_API_ENDPOINT);
  const timestamp = Date.now();
  const apiKey = generateApiKey(timestamp);
  return loginApi.create({
    apiKey,
    username,
    password,
    timestamp,
  }, {})
    .then(async (response) => {
      const alerDate = moment().add(constants.START_ALERT_IN_DAYS, 'days').unix();
      const nowDate = moment().unix();
      const { passwordExpiryDays, passwordExpiryTime } = response.data;
      const isPasswordExpired = nowDate > passwordExpiryTime && passwordExpiryTime !== -1;
      const expiresInDays = Math.ceil((passwordExpiryTime - nowDate) / (24 * 3600));
      localStorage.setItem('authenticated', true);
      const authType = (response.data && response.data.authType) ? response.data.authType : '';
      localStorage.setItem('authType', authType);
      localStorage.setItem('loginType', 'classiclogin');
      dispatch(dataChanged({
        [constants.AUTH_TYPE]: authType,
      }));
      await dispatch(boundSuccess('data', {
        ...response.data,
        username,
        isAuthenticated: true,
        isPasswordExpired,
        authType,
      }));
      await dispatch(setPasswordExpirySettings({
        passwordExpirationEnabled: passwordExpiryTime !== -1,
        expiresInDays,
        passwordExpiryDays,
        passwordExpiryTime,
      }));
      if (passwordExpiryDays && passwordExpiryTime < alerDate && passwordExpiryTime !== -1) {
        const pwdExpiryMsg = isPasswordExpired
          ? 'PASSWORD_EXPIRED_ALREADY'
          : 'Your password will be expired in ' + expiresInDays + ' days. You must change your password soon!';
        dispatch(notify(pwdExpiryMsg));
      }

      if (isPasswordExpired) return;
      dispatch(checkActivation());
      await dispatch(getOrgProvisioningInfo());
      // eslint-disable-next-line no-unused-vars
      const responsePermission = await permissionsApi.read()
        .then((perms) => {
          const { data, headers } = perms;
          const isReadOnly = (headers['x-zscaler-mode'] === 'read-only') || (authType === 'PARTNER_VIEW');
          if (isReadOnly) dispatch(notifyPersistent((authType === 'PARTNER_VIEW') ? 'SYSTEM_IN_READ_ONLY_MODE_ONLY' : 'SYSTEM_IN_READ_ONLY_MODE'));
          limitedPermission(data, isReadOnly);
          localStorage.setItem('apiPermissions', JSON.stringify(data));
          dispatch(dataChanged({
            [constants.API_PERMISSIONS]: data,
            [constants.ADMIN_RANK]: data.rank,
            [constants.READ_ONLY_MODE]: isReadOnly,
          }));
        })
        .catch((error) => {
          const errorText = get(error, 'response.data.message', 'Unable to login, please try again later');
          dispatch(boundError(errorText));
        });
      // eslint-disable-next-line no-unused-vars
      const responseConsigData = await configDataApi.read()
        .then((config) => {
          const { data } = config;
          localStorage.setItem('configData', JSON.stringify(data));
          dispatch(dataChanged({
            [constants.CONFIG_DATA]: data,
          }));
        })
        .catch((error) => {
          const errorText = get(error, 'response.data.message', 'Unable to login, please try again later');
          dispatch(boundError(errorText));
        });
      // eslint-disable-next-line no-unused-vars
      const responseSubscription = await subscriptionApi.read()
        .then((subs) => {
          const { data } = subs;
          localStorage.setItem('apiSubscriptions', JSON.stringify(data));
          dispatch(dataChanged({
            [constants.API_SUBSCRIPTIONS]: data,
          }));
        })
        .catch((error) => {
          const errorText = get(error, 'response.data.message', 'Unable to login, please try again later');
          dispatch(boundError(errorText));
        });
    })
    .catch((error) => {
      const errorText = get(error, 'response.data.message', 'Unable to login, please try again later');
      dispatch(boundError(errorText));
    });
};

/**
 * Have to enable this module once CSRF support is enabled from API
 */
// export const login = (username, password) => (dispatch) => {
//   dispatch(boundLoading({ loading: true }));
//   localStorage.removeItem('authenticated');
//   localStorage.removeItem('apiPermissions');

//   const permissionsApi = genericInterface(constants.PERMISSIONS_API_ENDPOINT);
//   const timestamp = Date.now();
//   const apiKey = generateApiKey(timestamp);

//   return getCSRFToken(dispatch)
//     .then(loginApi.create({
//       apiKey,
//       username,
//       password,
//       timestamp,
//     }, {}))
//     .then((response) => {
//       localStorage.setItem('authenticated', true);
//       PersistentStorage.setItem({ [LS_LOGIN]: true });
//       dispatch(closeNotification());
//       dispatch(boundSuccess('data', { ...response.data, isAuthenticated: true }));
//       permissionsApi.read()
//         .then((perms) => {
//           const { data } = perms;
//           localStorage.setItem('apiPermissions', JSON.stringify(data));
//           dispatch(dataChanged({
//             [constants.API_PERMISSIONS]: data,
//           }));
//         })
//         .catch((error) => {
//           const errorText = get(error, 'response.data.message',
//             'Unable to login, please try again later');
//           dispatch(boundError(errorText));
//         });
//     })
//     .catch((error) => {
//       // eslint-disable-next-line max-len
//       // const errorText = get(error, 'response.data.message',
//                'Unable to login, please try again later');
//       // dispatch(boundError(errorText));
//       const ERROR_CODES = [401, 400];
//       const errorStatus = get(error, 'response.status', 0);
//       let err = 'UNABLE_TO_LOGIN_TRY_AGAIN';
//       if (ERROR_CODES.indexOf(errorStatus) !== -1) {
//       err = errorStatus === 401
//          ? PersistentStorage.getItem(LS_ERR_MESSAGE)
//          : 'INVALID_INPUT_ARGUMENT';
//       }
//       dispatch(boundError(err));
//       PersistentStorage.removeItem(LS_ERR_MESSAGE);
//       throw err;
//     });
// };

export const refreshLoginInformation = () => (dispatch) => {
  dispatch(boundLoading({ loading: true }));
  localStorage.removeItem('authenticated');
  localStorage.removeItem('configData');
  localStorage.removeItem('apiPermissions');
  const configDataApi = genericInterface(constants.CONFIG_DATA_API_ENDPOINT);
  const permissionsApi = genericInterface(constants.PERMISSIONS_API_ENDPOINT);
  const subscriptionApi = genericInterface(constants.SUBSCRIPTIONS_API_ENDPOINT);
  // This will make sure CSRF Token is generated with Fetch
  PersistentStorage.removeItem(LS_CSRF_TOKEN);
  return loginApi.read(null)
    .then(async (response) => {
      // Checking for 401 error. simple return false for prevention of further calls
      if (response && response.data && isEmpty(response.data.loginName)) {
        PersistentStorage.setItem({ [LS_LOGOUT]: true });
        dispatch(boundLoading({ [constants.DATA_LOADING]: false }));
        return Promise.resolve(false);
      }
      if (localStorage.getItem('loginType') && localStorage.getItem('loginType').indexOf('classiclogin') > -1) {
        localStorage.setItem('loginType', 'classiclogin');
      } else {
        localStorage.setItem('loginType', 'zslogin');
      }
      PersistentStorage.removeItem(LS_LOGOUT);
      saveCsrfToken(response);
      localStorage.setItem('authenticated', true);
      PersistentStorage.setItem({ [LS_USERNAME]: response.data.loginName });
      const authType = (response.data && response.data.authType) ? response.data.authType : '';
      localStorage.setItem('authType', authType);
      dispatch(dataChanged({
        [constants.AUTH_TYPE]: authType,
      }));
      
      // dispatch(getPasswordExpirySettings());
      dispatch(boundSuccess('data', {
        ...response.data,
        username: response.data.loginName,
        isAuthenticated: true,
        isPasswordExpired: false,
        authType,
      }));

      permissionsApi.read()
        .then((perms) => {
          const { data, headers } = perms;
          const isReadOnly = (headers['x-zscaler-mode'] === 'read-only') || (authType === 'PARTNER_VIEW');
          if (isReadOnly) dispatch(notifyPersistent((authType === 'PARTNER_VIEW') ? 'SYSTEM_IN_READ_ONLY_MODE_ONLY' : 'SYSTEM_IN_READ_ONLY_MODE'));
          limitedPermission(data, isReadOnly);
          localStorage.setItem('apiPermissions', JSON.stringify(data));
          dispatch(dataChanged({
            [constants.API_PERMISSIONS]: data,
            [constants.ADMIN_RANK]: data.rank,
            [constants.READ_ONLY_MODE]: isReadOnly,
          }));
        })
        .catch((error) => {
          const errorText = get(error, 'response.data.message', 'Unable to login, please try again later');
          dispatch(boundError(errorText));
        });
        
      configDataApi.read()
        .then((config) => {
          const { data } = config;
          localStorage.setItem('configData', JSON.stringify(data));
          dispatch(dataChanged({
            [constants.CONFIG_DATA]: data,
          }));
        })
        .catch((error) => {
          const errorText = get(error, 'response.data.message', 'Unable to login, please try again later');
          dispatch(boundError(errorText));
        });

      subscriptionApi.read()
        .then((subs) => {
          const { data } = subs;
          localStorage.setItem('apiSubscriptions', JSON.stringify(data));
          dispatch(dataChanged({
            [constants.API_SUBSCRIPTIONS]: data,
          }));
        })
        .catch((error) => {
          const errorText = get(error, 'response.data.message', 'Unable to login, please try again later');
          dispatch(boundError(errorText));
        });

      dispatch(checkActivation());
      await dispatch(getOrgProvisioningInfo());

      return response && !isEmpty(response);
    })
    .catch(() => {
      dispatch(boundLoading({ [constants.DATA_LOADING]: false }));
      // To avoid memory leak after page returns to login page on 401
      return false;
    });
};

// Safe version for library usage that doesn't make API calls
export const refreshOneUILibrary = () => (dispatch) => {
  dispatch(boundLoading({ loading: true }));

  // Set some default state for library usage
  dispatch(dataChanged({
    [constants.API_PERMISSIONS]: {},
    [constants.ADMIN_RANK]: 'user',
    [constants.READ_ONLY_MODE]: false,
    [constants.CONFIG_DATA]: {},
    [constants.API_SUBSCRIPTIONS]: {},
  }));

  dispatch(boundLoading({ [constants.DATA_LOADING]: false }));

  // Return a resolved promise to maintain compatibility
  return Promise.resolve(true);
};

export const refreshOneUI = () => (dispatch) => {
  // Check if we're in a browser environment with proper API endpoints
  if (typeof window === 'undefined') {
    return dispatch(refreshOneUILibrary());
  }

  dispatch(boundLoading({ loading: true }));

  // Safe localStorage access
  try {
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem('configData');
      localStorage.removeItem('apiPermissions');
    }
  } catch (error) {
    console.warn('localStorage not available:', error);
  }

  const configDataApi = genericInterface(constants.CONFIG_DATA_API_ENDPOINT);
  const permissionsApi = genericInterface(constants.PERMISSIONS_API_ENDPOINT);
  const subscriptionApi = genericInterface(constants.SUBSCRIPTIONS_API_ENDPOINT);
  // This will make sure CSRF Token is generated with Fetch
  // PersistentStorage.removeItem(LS_CSRF_TOKEN);
  return permissionsApi.read()
    .then(async (response) => {
      // Checking for 401 error. simple return false for prevention of further calls
      // if (response && response.data && isEmpty(response.data.loginName)) {
      //   PersistentStorage.setItem({ [LS_LOGOUT]: true });
      //   dispatch(boundLoading({ [constants.DATA_LOADING]: false }));
      //   return Promise.resolve(false);
      // }
      // if (localStorage.getItem('loginType') && localStorage.getItem('loginType').indexOf('classiclogin') > -1) {
      //   localStorage.setItem('loginType', 'classiclogin');
      // } else {
      //   localStorage.setItem('loginType', 'zslogin');
      // }
      // PersistentStorage.removeItem(LS_LOGOUT);
      // saveCsrfToken(response);
      // localStorage.setItem('authenticated', true);
      // PersistentStorage.setItem({ [LS_USERNAME]: response.data.loginName });
      // const authType = (response.data && response.data.authType) ? response.data.authType : '';
      // localStorage.setItem('authType', authType);
      // dispatch(dataChanged({
      //   [constants.AUTH_TYPE]: authType,
      // }));
      
      // dispatch(getPasswordExpirySettings());
      // dispatch(boundSuccess('data', {
      //   ...response.data,
      //   username: response.data.loginName,
      //   isAuthenticated: true,
      //   isPasswordExpired: false,
      //   // authType,
      // }));
      dispatch(boundLoading({ [constants.DATA_LOADING]: false }));
      const { headers } = response;
      const isReadOnly = (headers['x-zscaler-mode'] === 'read-only');
      if (isReadOnly) dispatch(notifyPersistent('SYSTEM_IN_READ_ONLY_MODE'));
      limitedPermission(response.data, isReadOnly);

      // Safe localStorage access
      try {
        if (typeof localStorage !== 'undefined') {
          localStorage.setItem('apiPermissions', JSON.stringify(response.data));
        }
      } catch (error) {
        console.warn('localStorage not available:', error);
      }
      dispatch(dataChanged({
        [constants.API_PERMISSIONS]: response.data,
        [constants.ADMIN_RANK]: response.data?.rank,
        [constants.READ_ONLY_MODE]: isReadOnly,
      }));

      // permissionsApi.read()
      //   .then((perms) => {
      //     const { data, headers } = perms;
      //     const isReadOnly = (headers['x-zscaler-mode'] === 'read-only');
      //     if (isReadOnly) dispatch(notifyPersistent('SYSTEM_IN_READ_ONLY_MODE'));
      //     limitedPermission(data, isReadOnly);
      //     localStorage.setItem('apiPermissions', JSON.stringify(data));
      //     dispatch(dataChanged({
      //       [constants.API_PERMISSIONS]: data,
      //       [constants.ADMIN_RANK]: data.rank,
      //       [constants.READ_ONLY_MODE]: isReadOnly,
      //     }));
      //   })
      //   .catch((error) => {
      //     const errorText = get(error, 'response.data.message', 'Unable to login, please try again later');
      //     dispatch(boundError(errorText));
      //   });
        
      configDataApi.read()
        .then((config) => {
          const { data } = config;
          // Safe localStorage access
          try {
            if (typeof localStorage !== 'undefined') {
              localStorage.setItem('configData', JSON.stringify(data));
            }
          } catch (error) {
            console.warn('localStorage not available:', error);
          }
          dispatch(dataChanged({
            [constants.CONFIG_DATA]: data,
          }));
        })
        .catch((error) => {
          const errorText = get(error, 'response.data.message', 'Unable to login, please try again later');
          dispatch(boundError(errorText));
        });

      subscriptionApi.read()
        .then((subs) => {
          const { data } = subs;
          // Safe localStorage access
          try {
            if (typeof localStorage !== 'undefined') {
              localStorage.setItem('apiSubscriptions', JSON.stringify(data));
            }
          } catch (error) {
            console.warn('localStorage not available:', error);
          }
          dispatch(dataChanged({
            [constants.API_SUBSCRIPTIONS]: data,
          }));
        })
        .catch((error) => {
          const errorText = get(error, 'response.data.message', 'Unable to login, please try again later');
          dispatch(boundError(errorText));
        });

      dispatch(checkActivation());
      await dispatch(getOrgProvisioningInfo());

      return response && !isEmpty(response);
    })
    .catch(() => {
      dispatch(boundLoading({ [constants.DATA_LOADING]: false }));
      // To avoid memory leak after page returns to login page on 401
      return false;
    });
};

export const setLogout = () => ({
  type: actionTypes.LOGOUT,
  payload: {
    data: {
      [constants.ISAUTHENTICATED]: false,
    },
    error: '',
    loading: false,
    redirectHome: true,
  },
});

export const resetStore = () => ({
  type: actionTypes.RESET_STORE,
});

export const sessionTimeout = () => (dispatch) => {
  localStorage.removeItem('authenticated');
  localStorage.removeItem('apiPermissions');
  localStorage.removeItem('username');
  dispatch(notifyError('SESSION_TIMED_OUT'));
};

export const logout = () => async (dispatch) => {
  dispatch(boundLoading({ ...constants.RESOURCE_DEFAULTS, loading: true }));
  localStorage.removeItem('authenticated');
  localStorage.removeItem('apiPermissions');
  localStorage.removeItem('username');

  const logoutApi = genericInterface(constants.API_ENDPOINT);
  await logoutApi.del()
    .then(() => {
      PersistentStorage.clearSessionItems();
      dispatch(dataChanged({
        loading: false,
        [constants.REDIRECT_HOME]: true,
      }));
      dispatch(setLogout());
    })
    .catch((error) => {
      const errorText = error.message || get(error, 'response.data.code', 'Unable to logout, please try again later');
      dispatch(boundError(errorText));
    });
  dispatch(resetStore());
  return logoutApi;
};

export const getLocaleObj = (locale) => {
  return constants.LOCALES[locale];
};

export const setLocale = (dataID) => (dispatch) => {
  const selectedLocale = dataID.value;
  const localeObject = getLocaleObj(selectedLocale);
  const localeStr = constants.DATA_LOCALE;

  PersistentStorage.setItem({ [LS_LOCALE]: selectedLocale });
  i18n.changeLanguage(selectedLocale);
  localStorage.setItem(localeStr, selectedLocale);
  moment.locale(selectedLocale || 'en-US');

  dispatch({
    type: actionTypes.SET_LOCALE,
    payload: { locale: localeObject },
  });
};

export const setRememberUser = (event) => (dispatch) => {
  const checkboxValue = event.target.checked;
  localStorage.setItem('rememberUser', checkboxValue);

  if (!checkboxValue) {
    localStorage.removeItem('username');
  }

  dispatch({
    type: actionTypes.SET_REMEMBER_USER_ID,
    payload: { rememberUser: checkboxValue },
  });
};

export const checkSession = () => () => {
  // dispatch(loading());

  // return axios
  //   .get('/falcon/wapi/v1/auth', {
  //     headers: { 'auth-token': authToken },
  //     timeout: 10000,
  //     withCredentials: true,
  //   })
  //   .then((response) => {
  //     dispatch(setCheckSessionSuccess(response.data));
  //     dispatch(loaded());
  //   })
  //   .catch((error) => {
  //     dispatch(setLoginError(error.message));
  //     dispatch(loaded());
  //   });
};

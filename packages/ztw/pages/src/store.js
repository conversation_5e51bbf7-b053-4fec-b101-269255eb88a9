import { configureStore } from '@reduxjs/toolkit';

// A simple and basic reducer
const initialState = {
  counter: 0,
};

const counterReducer = (state = initialState, action) => {
  switch (action.type) {
    case 'INCREMENT':
      return { ...state, counter: state.counter + 1 };
    case 'DECREMENT':
      return { ...state, counter: state.counter - 1 };
    case 'RESET':
      return { ...state, counter: 0 };
    default:
      return state;
  }
};

// Configure the Redux store with the simple reducer
const store = configureStore({
  reducer: {
    counter: counterReducer,
  },
});

export default store;
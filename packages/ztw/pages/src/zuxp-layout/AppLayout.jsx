import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

// import Navbar from 'components/navbar';
import Notification from 'components/notification/Notification';
// import Help from 'components/help';
// import Footer from 'components/footer';
// import { Outlet, useLocation } from 'react-router-dom';
// import PasswordExpiryModal from 'components/PasswordExpiryModal';
import Splashscreen from './splashscreen';
// import LogoutSpinner from './LogoutSpinner';
import EUSAConfirmationModal from './eusa';
import EUSABanner from './eusa/EUSABanner';
import Spinner from 'components/spinner';
import { useDispatch } from 'react-redux';
import { refreshOneUI } from 'ducks/login';

function AppLayout({ children, skipInitialization = false }) {
  // const location = useLocation();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(!skipInitialization);
  const [isClient, setIsClient] = useState(false);

  // Handle client-side hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    // Skip initialization if requested or if not on client side
    if (skipInitialization || !isClient) {
      setLoading(false);
      return;
    }

    // Only run initialization on client side and when needed
    const initializeApp = async () => {
      try {
        const response = await dispatch(refreshOneUI());
        if (response !== undefined) {
          setLoading(false);
        }
      } catch (error) {
        console.warn('AppLayout initialization failed:', error);
        // Continue anyway to prevent blocking the UI
        setLoading(false);
      }
    };

    initializeApp();
  }, [dispatch, skipInitialization, isClient]);

  // Show spinner only if we're actually loading
  if (loading) {
    return <Spinner />;
  }

  return (
    <div className="page">
      <Notification />
      <EUSAConfirmationModal />
      <EUSABanner />
      <Splashscreen />
      <div id="noprint" className="noprint page">
        {/* <LogoutSpinner /> */}
        <>
          {children}
        </>
      </div>
      {/* <PasswordExpiryModal callTogglePasswordExpiryChangeListener={(str) => str} /> */}
    </div>
  );
}

AppLayout.propTypes = {
  children: PropTypes.any,
  skipInitialization: PropTypes.bool,
  classes: PropTypes.shape({}),
};

AppLayout.defaultProps = {
  skipInitialization: false,
  classes: {},
};

export default AppLayout;

'use client';
import React from 'react';
import { Provider } from 'react-redux';
import store from './../store';
// import AppLayout from './AppLayout';

function AppProvider({ children, }) {
  return (
    <>
      <div className="ec-root-page">
       <Provider store={store}>
          {/* <AppLayout>{children}</AppLayout> */}
          {children}
        </Provider>
      </div>
    </>
  );
}

export default AppProvider;

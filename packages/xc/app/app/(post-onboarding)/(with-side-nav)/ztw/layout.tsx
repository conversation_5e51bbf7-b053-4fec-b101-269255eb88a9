import dynamic from "next/dynamic";
import { type ReactNode } from "react";
import { Spinner } from "@/components/Spinner/Spinner";

type PageLayoutProps = {
  children: ReactNode;
};

const LayoutProvider = dynamic(() => import("./layoutProvider"), {
  ssr: false,
  loading: () => <Spinner size="2xl" defaultClass="min-h-[100vh]" />,
});

export default function Layout({ children }: PageLayoutProps) {
  return <LayoutProvider>{children}</LayoutProvider>;
}

"use client";
import { type ReactNode } from "react";
// eslint-disable-next-line import/no-unresolved
// import { initializeHttpClient } from "@ztw/pages"; // Keep imports that don't require dynamic loading
// import { getBearerToken } from "@up/std";
// import environment from "@/utils/environment";

// eslint-disable-next-line import/no-unresolved
import "@ztw/pages/dist/index.css";

// Initialize the HTTP client outside the component
// initializeHttpClient(environment, getBearerToken);

// eslint-disable-next-line import/no-unresolved
import { AppProvider } from "@ztw/pages";

type LayoutProviderProps = {
  children: ReactNode;
};

export default function LayoutProvider({ children }: LayoutProviderProps) {
  return <AppProvider>{children}</AppProvider>;
}
